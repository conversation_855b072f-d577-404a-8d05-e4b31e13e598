package com.score.callmetest.manager

import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.StrategyConfig
import com.score.callmetest.network.WallBroadcasterTag
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import java.util.ArrayList

object StrategyManager {

    /**
     * {
     * 	"isMatchCallFree": false,
     * 	"initTab": 0,
     * 	"isShowMatchGender": false,
     * 	"genderMatchCoin": {
     * 		"maleCoins": 0,
     * 		"femaleCoins": 0,
     * 		"bothCoins": 0,
     * 		"vipGoddessCoins": 25,
     * 		"goddessCoins": 30
     * 	},
     * 	"isReviewPkg": true,
     * 	"isShowLP": false,
     * 	"lpDiscount": 0,
     * 	"lpPromotionDiscount": 0,
     * 	"payChannels": ["GP"],
     * 	"isMaskOpen": true,
     * 	"isShowBroadcasterRank": false,
     * 	"payScriptUserCoins": 40,
     * 	"payScriptTriggerSecond": 45,
     * 	"fakeBroadcasterPopupSecond": 30,
     * 	"isAutoAccept": false,
     * 	"showMeet": false,
     * 	"broadcasterWallTags": ["Pet"],
     * 	"liveWallTagList": [],
     * 	"tabType": 1,
     * 	"isOpenBroacasterInvitation": true,
     * 	"isOpenFlashChat": false,
     * 	"videoStreamCategory": ["Live", "Hot"],
     * 	"flashChatConfig": {
     * 		"isSwitch": false,
     * 		"isFreeCall": false,
     * 		"residueFreeCallTimes": 0
     * 	},
     * 	"isShowMatch": false,
     * 	"isNewTppUsable": false,
     * 	"userInvitation": {
     * 		"tipsTitle": "SHARE & EARN 100 COINS",
     * 		"tipsContent": "Share with a friend and earn 100 coins for free",
     * 		"popUpTitle": "Share with a friend and earn 100 coins for free",
     * 		"popUpContent": "Invite your friends to {packageName} and earn up to 100 free coins for each invited friend.",
     * 		"popUpBottom": "You'll earn 0 coins when your friend complete registration.\nYou'll earn 100 coins when your friend complete first purchase.",
     * 		"shareContent": "Join {packageName} and input my invitation code {invitationCode} to get 20 free coins! Enjoy live video chat at {invitationUrl}"
     * 	},
     * 	"topOfficialUserIds": [],
     * 	"reviewOfficialBlacklistUserIds": ["1302892211775602688", "1499216468028555264", "1464076471248224256", "1349987244005523456", "1291682940983574528", "1351074033617207296", "1295279702818291712", "1349925887432327168", "1334126717501046784", "1351101555734085632", "1285880626939035648", "1529110821915983872"],
     * 	"officialBlacklistUserIds": ["1347831975964180480", "1304351073493975040", "1322454720689864704", "1325722360338317312", "1325729213021552640", "1349925887432327168"],
     * 	"imIncentiveBlacklistUserIds": ["128329832844820300"],
     * 	"broadcasterFollowOfficialUserIds": ["1744612431927312384", "1351101555734085632", "1433684162828697600", "1291682940983574528"],
     * 	"isDisplayNotDisturbCall": true,
     * 	"isDisplayNotDisturbIm": true,
     * 	"imSessionBalance": 2000,
     * 	"isShowFlowInfo": true,
     * 	"isShowDeletedButton": true,
     * 	"isShowLiveTab": false,
     * 	"webappUrl": "",
     * 	"minLiveUserLevel": 1,
     * 	"broadcasterWallTagList": [{
     * 		"tagName": "Popular",
     * 		"subTagList": ["Pet"],
     * 		"subTagInitIndex": 0
     * 	}],
     * 	"freeUserCallStaySecond": "20",
     * 	"freeUserImStaySecond": "5",
     * 	"rechargeUserCallStaySecond": "20",
     * 	"rechargeUserImStaySecond": "10",
     * 	"isRandomUploadPaidEvents": false,
     * 	"isSwitchIMLimit": true,
     * 	"isSwitchOneKeyFollow": true,
     * 	"isSwitchIMIncentive": false,
     * 	"isSwitchClub": false,
     * 	"isShowRookieGuide": false,
     * 	"isSwitchStrongGuide": false,
     * 	"isCallRearCamera": true,
     * 	"isCallCameraClose": true,
     * 	"isShowAutoTranslate": true,
     * 	"isSilence": false,
     * 	"isRearCamera": false,
     * 	"isCloseCamera": false,
     * 	"isSwitchInstruct": false,
     * 	"isForceEvaluationInstruct": false,
     * 	"isSwitchExtraCategory": false,
     * 	"isSwitchMultipleCall": false,
     * 	"timestamp": "*************",
     * 	"sayHiMaxCount": 8,
     * 	"sayHiQuickPhrases": ["Hello sexy girl~", "Show me baby,Baby, can you send the video?", "You look so hot\uD83D\uDD25"],
     * 	"userServiceAccountId": "1351074033617207296",
     * 	"broadcasterWallRegions": [],
     * 	"userMultipleLevel": 4,
     * 	"isReportFB": true,
     * 	"isEnableGuardian": false,
     * 	"isEnableCallCard": false,
     * 	"isOpenSpeechToText": false,
     * 	"voiceToTextConfig": {
     * 		"voiceToTextSwitch": false,
     * 		"voiceToTextUnitPrice": 5
     * 	},
     * 	"isEnableGroupSend": true,
     * 	"supportShowRoom": false,
     * 	"newRelationMsgSizeLimit": 5,
     * 	"unansweredGreetingExpireTTLHour": 1,
     * 	"isOpenFlashChatOnRole": false,
     * 	"broadcasterOnlineButton": "video",
     * 	"indiaWallCallButtonUI": "newUI",
     * 	"indiaWallLowCallUI": "oldLowCallUI",
     * 	"indiaRecommendShow": "new",
     * 	"indianWallUnlock": "unlock_india",
     * 	"callingSeconds": 90,
     * 	"isGuideInMode": false,
     * 	"appPraiseSwitch": true,
     * 	"liveComboLimitTime": 200,
     * 	"hideCountryFromLive": false,
     * 	"highLightUnreadOfficialUserIds": ["1744612431927312384"],
     * 	"videoCallConfig": {
     * 		"callNoticeContent": [
     * 			["Hi,love~", "Hi,baby", "Hi,what are you doing", "Hi,can we talk about something?", "Hi,how are you handsome~", "Wow,hi"],
     * 			["Are you new here?", "What can I do for you love", "Do you wanna to see me something?"]
     * 		],
     * 		"firstSendDelaySecond": 3,
     * 		"sendDelayMinSecond": 4,
     * 		"sendDelayMaxSecond": 8
     * 	},
     * 	"liveLevelEquityH5Url": "http://test-h5.isugar.xyz/userLevelbenefits/index.html?pkgName=com.score.callmetest&token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxOTM3MDQ3OTYwNjQ2OTc1NDg4IiwidXNlcl90eXBlIjoxLCJleHAiOjQ5MDY0Mjg0OTYsImNyZWF0ZWQiOjE3NTA3NTQ4OTYzMzB9.I_pzlOGHrv42-_XbXyUshroJeHCLwRztTYYrcuukf8vMISSPmvLHrD1gDzfsDJIPGx-6UTa6CWSDRKdt-g66NQ",
     * 	"activityListH5Url": "http://test-h5.isugar.xyz/eventNotice/index.html?pkgName=com.score.callmetest&token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxOTM3MDQ3OTYwNjQ2OTc1NDg4IiwidXNlcl90eXBlIjoxLCJleHAiOjQ5MDY0Mjg0OTYsImNyZWF0ZWQiOjE3NTA3NTQ4OTYzMzB9.I_pzlOGHrv42-_XbXyUshroJeHCLwRztTYYrcuukf8vMISSPmvLHrD1gDzfsDJIPGx-6UTa6CWSDRKdt-g66NQ",
     * 	"userSvipEquityH5Url": "http://test-h5.isugar.xyz/svipDetail/index.html?pkgName=com.score.callmetest&token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxOTM3MDQ3OTYwNjQ2OTc1NDg4IiwidXNlcl90eXBlIjoxLCJleHAiOjQ5MDY0Mjg0OTYsImNyZWF0ZWQiOjE3NTA3NTQ4OTYzMzB9.I_pzlOGHrv42-_XbXyUshroJeHCLwRztTYYrcuukf8vMISSPmvLHrD1gDzfsDJIPGx-6UTa6CWSDRKdt-g66NQ",
     * 	"blindBoxH5Url": "http://test-h5.isugar.xyz/activity/#/randomGift?pkgName=com.score.callmetest&token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxOTM3MDQ3OTYwNjQ2OTc1NDg4IiwidXNlcl90eXBlIjoxLCJleHAiOjQ5MDY0Mjg0OTYsImNyZWF0ZWQiOjE3NTA3NTQ4OTYzMzB9.I_pzlOGHrv42-_XbXyUshroJeHCLwRztTYYrcuukf8vMISSPmvLHrD1gDzfsDJIPGx-6UTa6CWSDRKdt-g66NQ",
     * 	"isShowWeeklyCard": false,
     * 	"multiGuestConfig": {},
     * 	"isSwitchSubscription": true,
     * 	"openLiveUnderReview": true,
     * 	"h5TaskUrl": "http://test-h5.isugar.xyz/h5HostessTask/index.html",
     * 	"isCR": false,
     * 	"userLiveCountryTagConfigList": [],
     * 	"isNo1V1": false,
     * 	"comboCountdown": 5,
     * 	"popByGroupCoins": 30000,
     * 	"globalNoticeChatRoomConfig": {
     * 		"broadcasterChatRoom": {
     * 			"roomNo": "broadcaster_global_notification_room"
     * 		},
     * 		"userChatRoom": {
     * 			"roomNo": "user_global_notification_room"
     * 		}
     * 	},
     * 	"vipServiceAccount": "1291682940983574528",
     * 	"imSessionBroadcasterIds": []
     * }
     */
    var strategyConfig: StrategyConfig? = null

    fun getStrategy(
        forceRefresh: Boolean = false,
        onSuccess: (StrategyConfig) -> Unit,
        onError: (String) -> Unit
    ) {
        if (!forceRefresh && strategyConfig != null) {
            onSuccess(strategyConfig!!)
            return
        }
        ThreadUtils.runOnIO {
            try {
                val response = RetrofitUtils.dataRepository.getStrategyPostV2()
                val data = if(response is NetworkResult.Success) response.data else null
                if (data != null) {
                    strategyConfig = data
                    onSuccess(data)
                } else {
                    onError("策略数据为空")
                }
            } catch (e: Exception) {
                onError("策略请求异常: ${e.localizedMessage}")
            }
        }
    }

    fun getBroadcasterWallTagList(): List<WallBroadcasterTag>? {
        return strategyConfig?.broadcasterWallTagList?.filter { wallBroadcasterTag ->
            // Style不展示
            wallBroadcasterTag.tagName != "Style"
        }
    }

    /**
     * 是否审核模式
     */
    fun isReviewPkg(): Boolean {
//        return true
        return strategyConfig?.isReviewPkg != false
    }


    // 审核模式机器人
    var reviewPkgUsers= mutableSetOf<String>()
    val REVIEW_USERS = "review_users"
    fun addReviewedUsers(userid: String) {
        if (isReviewPkg()) {
            reviewPkgUsers.add(userid)
            val edit = SharePreferenceUtil.getPrefs().edit()
            edit.putStringSet(REVIEW_USERS, reviewPkgUsers.toSet())
            edit.apply()
        }
    }

    fun readReviewedUsers() {
        if (isReviewPkg()) {
            val sp = SharePreferenceUtil.getPrefs()
            val savedUsers = sp.getStringSet(REVIEW_USERS, mutableSetOf<String>())
            reviewPkgUsers = savedUsers?.toMutableSet() ?: mutableSetOf()
        }
    }

    /**
     * 判断是否为置顶的官方用户
     * @param userId 用户ID
     * @param isFilter 是否需要进行过滤---过滤掉官方黑名单用户和审核模式黑名单用户
     * @return 如果是置顶官方用户返回true，否则返回false
     */
    fun isTopOfficialUser(userId: String?,isFilter: Boolean = false): Boolean {
        if(isFilter) {
            // 如果是官方黑名单用户，则不是置顶官方用户
            if (isOfficialBlacklistUser(userId)) {
                return false
            }
            // 如果当前是审核模式，并且该用户在审核模式黑名单中，则不是置顶官方用户
            if (isReviewOfficialBlacklistUser(userId) && isReviewPkg()) {
                return false
            }
        }
        // 判断用户是否在置顶官方用户列表中
        return strategyConfig?.topOfficialUserIds?.contains(userId) == true
    }

    /**
     * 判断是否为官方黑名单用户
     * @param userId 用户ID
     * @return 如果是官方黑名单用户返回true，否则返回false
     */
    fun isOfficialBlacklistUser(userId: String?): Boolean {
        return strategyConfig?.officialBlacklistUserIds?.contains(userId) == true
    }

    /**
     * 判断是否为审核模式下的黑名单用户
     * @param userId 用户ID
     * @return 如果是审核模式下的黑名单用户返回true，否则返回false
     */
    fun isReviewOfficialBlacklistUser(userId: String?): Boolean {
        return strategyConfig?.reviewOfficialBlacklistUserIds?.contains(userId) == true
    }

}