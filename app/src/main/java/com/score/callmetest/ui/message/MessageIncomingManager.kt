package com.score.callmetest.ui.message

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.entity.MessageType
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.im.callback.ImOnReceiveMessageListener
import com.score.callmetest.im.entity.HyperLinkMsg
import com.score.callmetest.im.entity.SingleJsonMsg
import com.score.callmetest.manager.AppLifecycleManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.main.CoinStoreActivity
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.ui.videocall.CallIncomingManager
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeUtils
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import io.rong.message.FileMessage
import io.rong.message.HQVoiceMessage
import io.rong.message.ImageMessage
import io.rong.message.TextMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.lang.ref.WeakReference

/**
 * 消息弹窗管理器
 * 管理收到新融云message事件时的弹窗显示
 *
 * 弹窗会在以下条件下自动显示：
 * 1. 收到新的融云消息
 * 2. 不是当前用户发送的消息
 * 3. 应用在前台
 * 4. 当前不在聊天页面
 * 
 * <AUTHOR>
 * @date 2025/07/30
 */
object MessageIncomingManager : ImOnReceiveMessageListener {

    private const val TAG = "MessageIncomingManager"
    private const val AUTO_DISMISS_DELAY = 3000L // 3秒后自动消失

    // 弹窗引用
    private var popupViewRef: WeakReference<MessageIncomingPopupView>? = null
    private var dialogFragmentRef: WeakReference<MessageIncomingDialogFragment>? = null

    // 状态管理
    private var isShowing = false
    private var currentMessage: Message? = null

    // 自动消失定时器
    private var autoDismissJob: Job? = null
    private val coroutineScope = MainScope()

    // 是否已经初始化
    private var isInitialized = false

    // MsgListFragment状态管理
    private var msgListFragmentVisible = false

    // 消息时间管理
    private var lastMessageTime = 0L
    private const val MESSAGE_EXPIRE_TIME = 30 * 60 * 1000L // 30分钟

    // 消息展示状态管理
    private val shownMessageIds = mutableSetOf<String>() // 已展示的消息ID集合
    private val backgroundReceivedMessages = mutableListOf<Message>() // 后台接收的消息列表

    // 消息列表回调
    private var messageListCallback: ((MessageListEntity) -> Unit)? = null

    // 聊天消息回调
    private var chatMessageCallback: ((Message) -> Unit)? = null

    /**
     * 初始化消息监听
     * 只需要调用一次
     */
    fun initialize() {
        if (isInitialized) {
            Timber.tag(TAG).d("MessageIncomingManager already initialized")
            return
        }

        Timber.tag(TAG).d("Initializing MessageIncomingManager")

        // 添加融云消息监听器
        RongCloudManager.addOnReceiveMessageListener(this)

        // 设置APP前台切换监听器
        AppLifecycleManager.setForegroundListener {
            ThreadUtils.runOnBackground {
                onAppBecameForeground()
            }
        }

        // 定期清理过期数据
        cleanupExpiredShownMessageIds()

        isInitialized = true
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        if (!isInitialized) return

        Timber.tag(TAG).d("Cleaning up MessageIncomingManager")

        // 移除融云消息监听器
        RongCloudManager.removeOnReceiveMessageListener(this)

        // 清理APP前台切换监听器
        AppLifecycleManager.setForegroundListener(null)

        // 隐藏弹窗
        hideIncomingMessage()

        // 清理消息状态
        shownMessageIds.clear()
        backgroundReceivedMessages.clear()
        currentMessage = null
        lastMessageTime = 0L

        // 清理回调
        messageListCallback = null
        chatMessageCallback = null

        isInitialized = false
    }
    
    /**
     * 融云消息接收回调
     */
    override fun onReceiveMessage(message: Message) {
        // 生成消息唯一ID
        val messageId = generateMessageId(message)
        val (messageType, displayContent) = getMessageTypeAndContent(message)

        Timber.tag(TAG).d("Received ${messageType?.name ?: "UNKNOWN"} message from ${message.senderUserId}: $displayContent, messageId: $messageId")
        if(message.senderUserId == UserInfoManager.myUserInfo?.userId){
            // 收到自己消息。。。improbable！但是还是出现了。。
            return
        }

        if (messageType == MessageType.SYSTEM) {
            // 客服消息--不处理
            return
        }

        // 检查是否为官方黑名单用户，如果是则不处理该消息
        if (StrategyManager.isOfficialBlacklistUser(message.senderUserId)) {
            Timber.tag(TAG).d("Message from official blacklist user ${message.senderUserId}, ignoring")
            return
        }

        // 检查是否为审核包中的官方黑名单用户，如果是则不处理该消息
        if (StrategyManager.isReviewOfficialBlacklistUser(message.senderUserId) && StrategyManager.isReviewPkg()) {
            return
        }


        // 处理消息列表更新（无论是否显示弹窗都要更新消息列表）
        processMessageListUpdate(message, messageType, displayContent)

        // 处理聊天消息更新（如果当前在聊天页面且是对应用户的消息）-- 存储数据库在[processMessageListUpdate]中处理了
        processChatMessageUpdate(message)

        // 检查消息是否已经展示过
        if (shownMessageIds.contains(messageId)) {
            Timber.tag(TAG).d("Message already shown, ignoring: $messageId")
            return
        }

        // 检查应用是否在后台
        if (AppLifecycleManager.isAppInBackground()) {
            Timber.tag(TAG).d("App in background, adding to background received messages")
            // 应用在后台时，将消息添加到后台接收列表
            backgroundReceivedMessages.add(message)
            // 清理过期的后台消息（超过30分钟）
            cleanupExpiredBackgroundMessages()
            return
        }

        // 检查是否应该显示弹窗（实时消息）
        if (!shouldShowMessagePopup(message, isFromBackground = false)) {
            return
        }

        // 显示弹窗
        Timber.tag(TAG).d("begin show msg popup from onReceiveMessage")
        showMessagePopup(message, displayContent)
    }
    
    /**
     * 显示或更新消息弹窗
     */
    private fun showOrUpdateIncomingMessagePopup(
        context: Context,
        avatarUrl: String?,
        nickname: String?,
        content: String?
    ) {
        if (UserInfoManager.myUserInfo?.isSwitchNotDisturbIm == true) {
            return
        }
        
        Timber.tag(TAG).d("Showing/updating message popup for $nickname")

        // 如果已经在显示弹窗，则更新内容
        if (isShowing) {
            updatePopupContent(avatarUrl, nickname, content)
            // 重新启动自动消失定时器
            startAutoDismissTimer()
            return
        }

        // 显示新弹窗
        startAutoDismissTimer()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(context.applicationContext)) {
            // 无悬浮窗权限，使用DialogFragment方案
            if (context is FragmentActivity) {
                showDialogFragmentInternal(context, avatarUrl, nickname, content)
            } else {
                Timber.tag(TAG).w("Context is not FragmentActivity, cannot show DialogFragment")
            }
            return
        }

        // 有悬浮窗权限，使用WindowManager方案
        val appContext = context.applicationContext
        val windowManager = appContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        // 使用Activity Context而不是Application Context来创建包含AppCompatImageView的视图
        val popupView = MessageIncomingPopupView(context)

        popupView.bind(
            avatarUrl = avatarUrl,
            nicknameStr = nickname,
            contentStr = content,
            onMessageClick = { handleMessageClick() },
            onSwipeUp = { hideIncomingMessage() },
            onDragStart = { pauseAutoDismissTimer() },
            onDragEnd = { startAutoDismissTimer() }
        )

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or // 允许touch事件穿透
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or // 不影响状态栏
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS, // 允许超出屏幕边界
            android.graphics.PixelFormat.TRANSLUCENT
        )
        params.gravity = android.view.Gravity.TOP
        // 设置状态栏透明
        params.systemUiVisibility =
            android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN

        try {
            windowManager.addView(popupView, params)
            popupViewRef = WeakReference(popupView)
            isShowing = true
            Timber.tag(TAG).d("WindowManager popup shown successfully")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to show WindowManager popup")
        }
    }

    /**
     * 更新弹窗内容
     */
    private fun updatePopupContent(
        avatarUrl: String?,
        nickname: String?,
        content: String?
    ) {
        Timber.tag(TAG).d("Updating popup content for $nickname")

        // 更新WindowManager弹窗
        popupViewRef?.get()?.updateContent(avatarUrl, nickname, content)

        // 更新DialogFragment弹窗
        dialogFragmentRef?.get()?.updateContent(avatarUrl, nickname, content)
    }
    
    /**
     * 隐藏消息弹窗
     */
    fun hideIncomingMessage() {
        Timber.tag(TAG).d("Hiding incoming message popup")
        
        val popupView = popupViewRef?.get()
        val dialog = dialogFragmentRef?.get()
        
        if (isShowing && popupView != null) {
            try {
                val windowManager = popupView.context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                windowManager.removeView(popupView)
                Timber.tag(TAG).d("WindowManager popup removed")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Failed to remove WindowManager popup")
            }
            popupViewRef = null
            isShowing = false
        } else if (isShowing && dialog != null) {
            val dialog = dialogFragmentRef?.get()
            if (dialog != null && dialog.isAdded && !dialog.isDetached ) {
                dialog.dismissAllowingStateLoss()
            }
            dialogFragmentRef = null
            isShowing = false
            Timber.tag(TAG).d("DialogFragment dismissed")
        } else {
            isShowing = false
            dialogFragmentRef = null
            popupViewRef = null
        }
        
        cancelAutoDismissTimer()
        currentMessage = null
    }

    /**
     * 处理消息点击事件
     */
    private fun handleMessageClick() {
        val message = currentMessage ?: return
        val context = ActivityUtils.getTopActivity() ?: return

        Timber.tag(TAG).d("Message clicked, opening chat with ${message.senderUserId}")

        hideIncomingMessage()

        // 获取用户信息后跳转到聊天页面
        UserInfoManager.getUserInfo(message.senderUserId) { userInfo ->
            ThreadUtils.runOnMain {
                if (userInfo != null) {
                    ChatActivity.start(context, userInfo)
                } else {
                    Timber.tag(TAG).w("Failed to get user info for ${message.senderUserId}")
                }
            }
        }
    }

    /**
     * 检查是否应该在Activity中显示DialogFragment
     * 用于Activity切换时的弹窗恢复
     */
    fun shouldShowDialogInActivity(activity: FragmentActivity) {
        if (currentMessage != null && isShowing && !canDrawOverlays(activity)) {
            val message = currentMessage!!
            val textMessage = message.content as? TextMessage ?: return

            UserInfoManager.getUserInfo(message.senderUserId) { userInfo ->
                ThreadUtils.runOnMain {
                    if (userInfo != null) {
                        showDialogFragmentInternal(
                            activity = activity,
                            avatarUrl = userInfo.avatarThumbUrl ?: userInfo.avatar,
                            nickname = userInfo.nickname,
                            content = textMessage.content
                        )
                    }
                }
            }
        }
    }

    /**
     * 检查是否有悬浮窗权限
     */
    private fun canDrawOverlays(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context.applicationContext)
        } else {
            true
        }
    }

    /**
     * 显示DialogFragment弹窗
     */
    private fun showDialogFragmentInternal(
        activity: FragmentActivity,
        avatarUrl: String?,
        nickname: String?,
        content: String?
    ) {
        if (AppLifecycleManager.isAppInBackground()) {
            return
        }

        // 先关闭旧的，再展示新的
        dialogFragmentRef?.get()?.dismissAllowingStateLoss()

        val dialog = MessageIncomingDialogFragment.newInstance(avatarUrl, nickname, content)
        dialog.setOnActionListener(object : MessageIncomingDialogFragment.OnActionListener {
            override fun onMessageClick() {
                handleMessageClick()
            }

            override fun onSwipeUp() {
                hideIncomingMessage()
            }

            override fun onDragStart() {
                pauseAutoDismissTimer()
            }

            override fun onDragEnd() {
                startAutoDismissTimer()
            }
        })

        try {
            dialog.show(activity.supportFragmentManager, "message_incoming_dialog")
            dialogFragmentRef = WeakReference(dialog)
            isShowing = true
            Timber.tag(TAG).d("DialogFragment shown successfully")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to show DialogFragment")
        }
    }

    /**
     * 启动自动消失定时器
     */
    private fun startAutoDismissTimer() {
        autoDismissJob?.cancel()
        autoDismissJob = coroutineScope.launch {
            delay(AUTO_DISMISS_DELAY)
            Timber.tag(TAG).d("Auto dismiss timer triggered")
            hideIncomingMessage()
        }
    }

    /**
     * 取消自动消失定时器
     */
    private fun cancelAutoDismissTimer() {
        autoDismissJob?.cancel()
        autoDismissJob = null
    }

    /**
     * 暂停自动消失定时器
     */
    private fun pauseAutoDismissTimer() {
        Timber.tag(TAG).d("Pausing auto dismiss timer")
        autoDismissJob?.cancel()
        autoDismissJob = null
    }

    /**
     * 检查是否是顶部Activity
     */
    private fun isTopActivity(activity: FragmentActivity): Boolean {
        val am = activity.getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager ?: return true
        val runningTasks = am.getRunningTasks(1)
        if (runningTasks.isNullOrEmpty()) return true
        val topActivity = runningTasks[0].topActivity ?: return true
        return topActivity.className == activity.javaClass.name
    }

    /**
     * 获取当前聊天页面的目标用户ID
     */
    private fun getCurrentChatUserId(): String? {
        return ChatActivity.getCurrentChatUserId()
    }

    /**
     * 根据融云消息内容获取消息类型和显示内容
     * 参考ChatViewModel和MsgListAdapter的逻辑
     *
     * @param message 融云消息内容
     * @return Pair<MessageType?, String> 消息类型和显示内容
     */
    private fun getMessageTypeAndContent(message: Message?): Pair<MessageType?, String> {
        val messageContent = message?.content
        if(message?.senderUserId == StrategyManager.strategyConfig?.userServiceAccountId){
            // 系统--客服账号
            val displayText = CallmeApplication.context.getString(R.string.msg_service_welcome)
            return MessageType.SYSTEM to displayText
        }
        return when (messageContent) {
            is TextMessage -> {
                MessageType.TEXT to messageContent.content
            }
            is ImageMessage -> {
                val displayText = CallmeApplication.context.getString(R.string.msg_list_type_img)
                MessageType.IMAGE to displayText
            }
            is HQVoiceMessage -> {
                val displayText = CallmeApplication.context.getString(R.string.msg_list_type_voice)
                MessageType.VOICE to displayText
            }
            is HyperLinkMsg -> {
//                val displayText = CallmeApplication.context.getString(R.string.msg_list_type_link)
//                MessageType.LINK to displayText
                null to ""
            }
            is SingleJsonMsg -> {
                val displayText = CallmeApplication.context.getString(R.string.msg_list_type_gift)
                MessageType.GIFT to displayText
            }
            /*is FileMessage -> {
                val displayText = CallmeApplication.context.getString(R.string.msg_list_type_file)
                MessageType.FILE to displayText
            }*/
            // 预留其他消息类型
            else -> {
                // 不支持的消息类型
                null to ""
            }
        }
    }

    /**
     * 设置MsgListFragment的可见状态
     * 应在MsgListFragment的setUserVisibleHint或onHiddenChanged中调用
     */
    fun setMsgListFragmentVisible(visible: Boolean) {
        msgListFragmentVisible = visible
        Timber.tag(TAG).d("MsgListFragment visible state changed: $visible")

        // 如果MsgListFragment变为可见，隐藏当前的消息弹窗
        if (visible && isShowing) {
            Timber.tag(TAG).d("MsgListFragment became visible, hiding message popup")
            hideIncomingMessage()
        }
    }

    /**
     * 设置消息列表回调
     * 用于通知MsgListViewModel有新消息
     */
    fun setMessageListCallback(callback: ((MessageListEntity) -> Unit)?) {
        messageListCallback = callback
        Timber.tag(TAG).d("Message list callback set: ${callback != null}")
    }

    /**
     * 设置聊天消息回调
     * 用于通知ChatViewModel有新消息
     */
    fun setChatMessageCallback(callback: ((Message) -> Unit)?) {
        chatMessageCallback = callback
        Timber.tag(TAG).d("Chat message callback set: ${callback != null}")
    }

    /**
     * 检查MsgListFragment是否可见
     */
    private fun isMsgListFragmentVisible(): Boolean {
        return msgListFragmentVisible
    }

    /**
     * APP从后台切换到前台时调用
     * 检查是否需要显示后台接收的未展示消息弹窗
     */
    fun onAppBecameForeground() {
        Timber.tag(TAG).d("${Thread.currentThread()}...App became foreground, checking for background received messages")

        // 清理过期的后台消息
        cleanupExpiredBackgroundMessages()

        if (backgroundReceivedMessages.isEmpty()) {
            Timber.tag(TAG).d("No background received messages")
            return
        }

        // 获取最新的后台接收消息
        val latestMessage = backgroundReceivedMessages.lastOrNull()
        if (latestMessage == null) {
            Timber.tag(TAG).d("No valid background message")
            return
        }

        val messageId = generateMessageId(latestMessage)

        // 检查消息是否已经展示过
        if (shownMessageIds.contains(messageId)) {
            Timber.tag(TAG).d("Latest background message already shown: $messageId")
            return
        }

        // 检查是否应该显示弹窗（后台消息）
        if (!shouldShowMessagePopup(latestMessage, isFromBackground = true)) {
            return
        }

        // 获取消息类型和内容
        val (messageType, displayContent) = getMessageTypeAndContent(latestMessage)
        if (messageType == null) {
            Timber.tag(TAG).d("Unsupported message type for foreground popup")
            return
        }

        // 显示弹窗
        Timber.tag(TAG).d("begin show msg popup from onAppBecameForeground")
        showMessagePopup(latestMessage, displayContent)

        // 清空后台接收消息列表
        backgroundReceivedMessages.clear()
        Timber.tag(TAG).d("Cleared background received messages")
    }

    /**
     * 来电弹窗显示时，顶掉消息弹窗
     * 应在CallIncomingManager显示来电弹窗时调用
     */
    fun onCallIncomingShown() {
        if (isShowing) {
            Timber.tag(TAG).d("Call incoming shown, hiding message popup")
            hideIncomingMessage()
        }
    }

    /**
     * 检查是否应该显示消息弹窗
     * 统一的条件判断逻辑
     *
     * @param message 要检查的消息
     * @param isFromBackground 是否来自后台切换（用于区分实时消息和后台消息）
     * @return true表示应该显示弹窗，false表示不应该显示
     */
    private fun shouldShowMessagePopup(message: Message, isFromBackground: Boolean = false): Boolean {
        // 检查消息类型是否支持弹窗显示
        val (messageType, _) = getMessageTypeAndContent(message)
        if (messageType == null) {
            Timber.tag(TAG).d("Unsupported message type: ${message.content?.javaClass?.simpleName}")
            return false
        }

        if(messageType == MessageType.SYSTEM){
            // 系统消息
            Timber.tag(TAG).d("Message from current system, not showing popup")
            return false
        }

        // 检查是否是当前用户发送的消息
        val currentUserId = UserInfoManager.myUserInfo?.userId
        if (message.senderUserId == currentUserId) {
            Timber.tag(TAG).d("Message from current user, not showing popup")
            return false
        }

        // 检查应用是否在前台（只有实时消息需要检查，后台切换消息已经确定在前台）
        if (!isFromBackground && AppLifecycleManager.isAppInBackground()) {
            Timber.tag(TAG).d("App in background, not showing popup")
            return false
        }

        // 检查来电弹窗是否正在显示
        if (CallIncomingManager.isShowing()) {
            Timber.tag(TAG).d("Call incoming popup is showing, not showing message popup")
            return false
        }

        // 检查是否在需要屏蔽的页面
        val topActivity = ActivityUtils.getTopActivity()
        when(topActivity){
            is VideoCallActivity,
            is CoinStoreActivity -> {
                Timber.tag(TAG).d("In blocked activity: ${topActivity.javaClass.simpleName}")
                return false
            }
        }

        // 检查是否在消息列表页面
        if (isMsgListFragmentVisible()) {
            Timber.tag(TAG).d("MsgListFragment is visible, not showing popup")
            return false
        }

        // 检查是否在聊天页面且是当前聊天对象的消息
        if (topActivity is ChatActivity) {
            val currentChatUserId = getCurrentChatUserId()
            if (currentChatUserId == message.senderUserId) {
                Timber.tag(TAG).d("Message from current chat user, not showing popup")
                return false
            }
        }

        return true
    }

    /**
     * 显示消息弹窗的统一方法
     *
     * @param message 消息对象
     * @param displayContent 显示内容
     */
    private fun showMessagePopup(message: Message, displayContent: String) {
        // 更新当前消息和时间
        currentMessage = message
        lastMessageTime = System.currentTimeMillis()

        // 标记消息为已展示
        val messageId = generateMessageId(message)
        shownMessageIds.add(messageId)
        Timber.tag(TAG).d("Marked message as shown: $messageId")

        // 获取发送者信息并显示弹窗
        UserInfoManager.getUserInfo(message.senderUserId) { userInfo ->
            ThreadUtils.runOnMain {
                val context = ActivityUtils.getTopActivity() ?: return@runOnMain

                if (userInfo != null) {
                    showOrUpdateIncomingMessagePopup(
                        context = context,
                        avatarUrl = userInfo.avatarThumbUrl ?: userInfo.avatar,
                        nickname = userInfo.nickname,
                        content = displayContent
                    )
                } else {
                    // 如果获取不到用户信息，使用默认信息
                    // 直接不显示
                }
            }
        }
    }

    /**
     * 生成消息唯一ID
     * 基于发送者ID、消息内容和时间戳生成
     */
    private fun generateMessageId(message: Message): String {
        val content = when (val messageContent = message.content) {
            is TextMessage -> messageContent.content
            else -> messageContent?.javaClass?.simpleName ?: "unknown"
        }
        return "${message.senderUserId}_${content.hashCode()}_${message.sentTime}"
    }

    /**
     * 清理过期的后台消息
     * 移除超过30分钟的消息
     */
    private fun cleanupExpiredBackgroundMessages() {
        val currentTime = System.currentTimeMillis()
        val iterator = backgroundReceivedMessages.iterator()
        var removedCount = 0

        while (iterator.hasNext()) {
            val message = iterator.next()
            val messageAge = currentTime - message.sentTime
            if (messageAge > MESSAGE_EXPIRE_TIME) {
                iterator.remove()
                removedCount++
            }
        }

        if (removedCount > 0) {
            Timber.tag(TAG).d("Cleaned up $removedCount expired background messages")
        }
    }

    /**
     * 清理过期的已展示消息ID
     * 定期清理，避免内存泄漏
     */
    private fun cleanupExpiredShownMessageIds() {
        // 保留最近1000条记录，避免无限增长
        if (shownMessageIds.size > 1000) {
            val toRemove = shownMessageIds.size - 1000
            val iterator = shownMessageIds.iterator()
            repeat(toRemove) {
                if (iterator.hasNext()) {
                    iterator.next()
                    iterator.remove()
                }
            }
            Timber.tag(TAG).d("Cleaned up $toRemove old shown message IDs")
        }
    }

    /**
     * 处理消息列表更新
     * 将融云消息转换为MessageListEntity并更新数据库
     */
    private fun processMessageListUpdate(message: Message, messageType: MessageType?, displayContent: String) {
        if (messageType == null) {
            Timber.tag(TAG).d("Unsupported message type for message list update")
            return
        }

        Timber.tag(TAG).d("Processing message list update for user: ${message.senderUserId}")

        // 判断是否是在聊天页接收到的消息 && 当前聊天对象id==接收到message的sendUserId
        val isResetUnreadCount = ActivityUtils.getTopActivity() is ChatActivity && ChatActivity.getCurrentChatUserId() == message.senderUserId

        /** 是官方用户 */
        val isOfficialUser = StrategyManager.isTopOfficialUser(message.senderUserId,isFilter = true)

        // 先查询数据库是否已存在该用户的消息记录
        DatabaseFactory.getDatabase(CallmeApplication.context)
            .getMessageListById(message.senderUserId) { existingEntity ->
                if (existingEntity != null) {
                    // 存在 - 更新记录
                    updateExistingMessageListEntity(existingEntity, message, messageType, displayContent,isResetUnreadCount,isOfficialUser)
                } else {
                    // 不存在 - 创建新记录
                    createNewMessageListEntity(message, messageType, displayContent,isResetUnreadCount,isOfficialUser)
                }
            }
    }

    /**
     * 更新已存在的消息列表实体
     */
    private fun updateExistingMessageListEntity(
        existingEntity: MessageListEntity,
        message: Message,
        messageType: MessageType,
        displayContent: String,
        isResetUnreadCount: Boolean,
        isOfficialUser: Boolean
    ) {

        val updatedEntity = existingEntity.copy(
            lastMessage = displayContent,
            lastMessageType = messageType,
            onlineStatus = if(isOfficialUser) CallStatus.UNKNOWN else CallStatus.ONLINE, // 收到消息说明在线
            unreadCount = if(isResetUnreadCount) 0 else existingEntity.unreadCount + 1,
            timeInMillis = message.sentTime,
            timestamp = TimeUtils.formatTimestampForChatList(message.sentTime)
        )
        // 只有官方号手动强制置顶
        if(isOfficialUser) {
            updatedEntity.isPinned = true
        }

        // 不管数据库插入成功与否-都通知回调
        messageListCallback?.invoke(updatedEntity)
        // 更新数据库
        DatabaseFactory.getDatabase(CallmeApplication.context)
            .updateCurrentUserMessageList(updatedEntity) { success ->
                if (success) {
                    Timber.tag(TAG).d("Updated message list entity for user: ${message.senderUserId}")
                } else {
                    Timber.tag(TAG).e("Failed to update message list entity for user: ${message.senderUserId}")
                }
            }
    }

    /**
     * 创建新的消息列表实体
     */
    private fun createNewMessageListEntity(
        message: Message,
        messageType: MessageType,
        displayContent: String,
        isResetUnreadCount: Boolean,
        isOfficialUser: Boolean
    ) {

        // 获取发送者用户信息
        UserInfoManager.getUserInfo(message.senderUserId) { senderUserInfo ->
            if (senderUserInfo != null) {
                val newEntity = MessageListEntity(
                    userId = senderUserInfo.userId?:"",
                    currentUserId = UserInfoManager.myUserInfo?.userId?:"",
                    userName = senderUserInfo.nickname?:"",
                    gender = senderUserInfo.gender?:1,
                    unitPrice = senderUserInfo.unitPrice?:0,
                    avatar = senderUserInfo.avatar?:"",
                    avatarThumbUrl = senderUserInfo.avatarThumbUrl?:"",
                    lastMessage = displayContent,
                    lastMessageType = messageType,
                    timestamp = TimeUtils.formatTimestampForChatList(message.sentTime),
                    timeInMillis = message.sentTime,
                    unreadCount = if(isResetUnreadCount) 0 else 1,
                    onlineStatus = CallStatus.UNKNOWN,
                    isPinned = isOfficialUser,
                    isBottomView = false
                )

                // 不管数据库插入成功与否-都通知回调
                messageListCallback?.invoke(newEntity)
                // 插入数据库
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .insertMessageList(newEntity) { success ->
                        if (success) {
                            Timber.tag(TAG).d("Created new message list entity for user: ${message.senderUserId}")
                        } else {
                            Timber.tag(TAG).e("Failed to create message list entity for user: ${message.senderUserId}")
                        }
                    }
            } else {
                Timber.tag(TAG).w("Failed to get user info for message list creation: ${message.senderUserId}")
            }
        }
    }

    /**
     * 处理聊天消息更新
     * 如果当前在聊天页面且是对应用户的消息，通知ChatViewModel
     */
    private fun processChatMessageUpdate(message: Message) {
        val currentChatUserId = ChatActivity.getCurrentChatUserId()

        // 检查是否在聊天页面且消息来自当前聊天对象
        if (currentChatUserId != null &&
            (message.targetId == currentChatUserId || message.senderUserId == currentChatUserId)) {

            // 检查消息类型是否支持（与ChatViewModel中的过滤逻辑保持一致）
            val isSupported = when(message.content) {
                is TextMessage -> true
                is ImageMessage -> true
                is HQVoiceMessage -> true
                is SingleJsonMsg -> true
                is HyperLinkMsg -> true
                is FileMessage -> true
                else -> false
            }

            if (isSupported) {
                Timber.tag(TAG).d("Processing chat message update for current chat user: $currentChatUserId")
                // 通知ChatViewModel
                chatMessageCallback?.invoke(message)
            } else {
                Timber.tag(TAG).d("Unsupported message type for chat: ${message.content?.javaClass?.simpleName}")
            }
        }
    }
}
